import { Inject, Provide, Config, InjectClient } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { Utils } from '../../../../comm/utils';
import { PortalArticleEntity } from '../../entity/portal/Article';
import { CoolElasticSearch } from '@cool-midway/es';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';

import * as _ from 'lodash';
import { CommonForumService } from './forum';
import { TblTag } from '../../entity/tag/TblTag';
import { IwmsNews } from '../../entity/www/IwmsNews';
import { IwmsClass } from '../../entity/www/IwmsClass';
import { Article } from '../../entity/wwwbuffer/Article';
import { WaitingArticle } from '../../entity/wwwbuffer/WaitingArticle';
import { PortalCandidateArticleEntity } from '../../entity/portal/CandidateArticle';
import { PortalWaitingArticleEntity } from '../../entity/portal/WaitingArticle';
import axios from 'axios';
import * as cheerio from 'cheerio';

/**
 * Portal
 */
@Provide()
export class PortalService extends BaseService {
  @Inject()
  es: CoolElasticSearch;

  @InjectEntityModel(IwmsNews, 'www')
  iwmsNews: Repository<IwmsNews>;

  @InjectEntityModel(IwmsClass, 'www')
  iwmsClass: Repository<IwmsClass>;

  @InjectEntityModel(TblTag, 'id')
  tblTag: Repository<TblTag>;

  @InjectEntityModel(PortalArticleEntity)
  portalArticleEntity: Repository<PortalArticleEntity>;

  @InjectEntityModel(PortalCandidateArticleEntity)
  portalCandidateArticleEntity: Repository<PortalCandidateArticleEntity>;

  @InjectEntityModel(PortalWaitingArticleEntity)
  portalWaitingArticleEntity: Repository<PortalWaitingArticleEntity>;

  @InjectEntityModel(Article, 'wwwbuffer')
  wwwbufferArticle: Repository<Article>;

  @InjectEntityModel(WaitingArticle, 'wwwbuffer')
  wwwbufferWaitingArticle: Repository<WaitingArticle>;

  @Inject()
  commonForumService: CommonForumService;

  @Inject()
  utils: Utils;

  @Config('redisKey')
  redisKey;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  async portalPage(param: any) {
    const { page = 1, pageSize = 50, s } = param;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.portalArticleEntity
      .createQueryBuilder('portal')
      .orderBy('portal.displayorder', 'DESC')
      .addOrderBy('portal.id', 'DESC')
      .skip(skip)
      .take(pageSize);

    if (s) {
      queryBuilder.where('portal.title LIKE :title', { title: `%${s}%` });
    }

    return await queryBuilder.getManyAndCount();
  }

  async portalFindOne(param: any) {
    return await this.portalArticleEntity.findOneBy({
      id: param.id,
    });
  }

  async portalCreate(param: any) {
    param.datetime = this.utils.now();
    
    if (param.forumTid && !param.thread) {
      param.forumTid = this.utils.extractForumTid(param.forumTid);
    } else if(param.thread && (!param.fid || !param.typeid || !param.username || !param.uid)) {
      const thread: any = await this.commonForumService.publishToForum({
        fid: param.fid,
        typeid: param.typeid,
        username: param.username,
        uid: param.uid,
        subject: param.title,
        content: param.content,
      });

      param.forumTid = thread.tid;
    } else {
      param.forumTid = 0;
    }

    // Save to database
    const result = await this.portalArticleEntity.save(param);

    // Sync to Elasticsearch
    await this.es.client.index({
      index: 'portal_articles',
      id: result.id.toString(),
      body: {
        id: result.id,
        title: result.title,
        summary: result.summary,
        forumTid: result.forumTid,
        content: result.content?.replace(/<[^>]+>/g, ''),
        datetime: result.datetime,
        author: result.author,
        tags: this.convertStringToArray(result.tags),
        tagsValue: this.convertStringToArray(result.tagsVal),
        majorTags: this.convertStringToArray(result.majorTags),
        majorTagsValue: this.convertStringToArray(result.majorTagsVal),
        schoolTags: this.convertStringToArray(result.schoolTags),
        schoolTagsValue: this.convertStringToArray(result.schoolTagsVal),
        type: result.type,
        externalLink: result.externalLink
      }
    });

    return result;
  }

  async portalUpdate(param: any) {
    if (param.forumTid && !param.thread) {
      param.forumTid = this.utils.extractForumTid(param.forumTid);
    } else if(param.thread && (!param.fid || !param.typeid || !param.username || !param.uid)) {
      const thread: any = await this.commonForumService.publishToForum({
        fid: param.fid,
        typeid: param.typeid,
        username: param.username,
        uid: param.uid,
        subject: param.title,
        content: param.content,
      });

      param.forumTid = thread.tid;
    } else {
      param.forumTid = 0;
    }

    if(!param.isExternalLink) param.externalLink = '';
    
    delete param.isExternalLink;
    delete param.fid;
    delete param.typeid;
    delete param.uid;
    delete param.username;
    delete param.thread;

    // Update database
    await this.portalArticleEntity.update(param.id, {
      ...param,
    });

    // Update Elasticsearch
    await this.es.client.update({
      index: 'portal_articles',
      id: param.id.toString(),
      body: {
        doc: {
          title: param.title,
          summary: param.summary,
          content: param.content?.replace(/<[^>]+>/g, ''),
          datetime: param.datetime,
          forumTid: param.forumTid,
          author: param.author,
          tags: this.convertStringToArray(param.tags),
          tagsValue: this.convertStringToArray(param.tagsVal),
          majorTags: this.convertStringToArray(param.majorTags),
          majorTagsValue: this.convertStringToArray(param.majorTagsVal),
          schoolTags: this.convertStringToArray(param.schoolTags),
          schoolTagsValue: this.convertStringToArray(param.schoolTagsVal),
          type: param.type,
          externalLink: param.externalLink
        }
      }
    });
  }

  async portalDelete(param: any) {
    // Delete from database
    await this.portalArticleEntity.delete({ id: param.id });
    
    // Delete from Elasticsearch
    await this.es.client.delete({
      index: 'portal_articles',
      id: param.id.toString()
    })
  }

  async transferData() {
      // Clear the table before starting
      await this.portalArticleEntity.clear();
      // Disable auto-increment
      await this.portalArticleEntity.query('ALTER TABLE portal_article AUTO_INCREMENT = 1');
      await this.portalArticleEntity.query('SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO"');

      try {
          const iwmsNews = await this.iwmsNews.find();

          const batchSize = 1000;
          const articles = [];

          for (const item of iwmsNews) {
              const classPath = await this.getFullClassPath(item.classid);
              let tag = await this.getTagFromCategories(classPath);

              const { cleanContent, tid } = item.content
                  ? this.processContentAndExtractTid(item.content)
                  : { cleanContent: '', tid: 0 };

              const article = {
                  pic: 'https://mat.chasedream.com/chasedream/test/images/400x240/1.png',
                  id: item.articleid,
                  title: item.title || '',
                  content: cleanContent,
                  datetime: item.dateandtime ? this.utils.dateToTimeStamp(item.dateandtime) : 0,
                  author: item.author || '',
                  hits: item.hits || 0,
                  summary: item.summary || cleanContent.replace(/<[^>]+>/g, '').substring(0, 100),
                  category: (classPath && classPath[0]) || '',
                  tags: tag || '',
                  forumTid: tid
              };

              if (article.tags === '签证|经验|美国') {
                  article.tags = `${article.tags}|${this.utils.timestampToDate(article.datetime, 'YYYY')}`;
              } else if (classPath[1] === '行前准备') {
                  if (item.title.indexOf('体检') !== -1 || item.content.indexOf('体检') !== -1) {
                      article.tags = '签证|体检';
                  } else if (item.title.indexOf('疫苗') !== -1 || item.content.indexOf('疫苗') !== -1) {
                      article.tags = '签证|疫苗';
                  } else if (item.title.indexOf('档案') !== -1 || item.content.indexOf('档案') !== -1) {
                      article.tags = '签证|档案';
                  }
              }

              articles.push(article);

              // When batch size is reached, save to database only
              if (articles.length === batchSize) {
                  await this.portalArticleEntity.save(articles);
                  articles.length = 0;
              }
          }

          // Save any remaining articles to database
          if (articles.length > 0) {
              await this.portalArticleEntity.save(articles);
          }
      } finally {
          // Re-enable auto-increment
          await this.portalArticleEntity.query('SET SQL_MODE = ""');

          await this.portalArticleEntity.query(`UPDATE portal_article 
              SET summary = LEFT(
                  REGEXP_REPLACE(content, '<[^>]+>', ''),
                  20
              ) 
              WHERE (summary = '' OR summary IS NULL);`);

          await this.portalArticleEntity.query(`UPDATE portal_article
              SET tags = CASE 
                  WHEN tags = 'Master|申请|PS|Resume' THEN 'Master|PS|Resume'
                  WHEN tags = 'Master|申请|总结' THEN 'Master|申请总结'
                  WHEN tags = 'Master|申请|推荐信' THEN 'Master|推荐信'
                  WHEN tags = 'Master|申请|答疑' THEN 'Master|答疑'
                  WHEN tags = 'Master|申请|面试|经验' THEN 'Master|面试经验'
                  WHEN tags = 'MBA|申请|Essay' THEN 'MBA|Essay'
                  WHEN tags = 'MBA|申请|Resume' THEN 'MBA|Resume'
                  WHEN tags = 'MBA|申请|总结' THEN 'MBA|申请总结'
                  WHEN tags = 'MBA|申请|推荐信' THEN 'MBA|推荐信'
                  WHEN tags = 'MBA|申请|答疑' THEN 'MBA|答疑'
                  WHEN tags = 'MBA|申请|面试|经验' THEN 'MBA|面试经验'
                  WHEN tags = '博士|在读|经验' THEN '博士|读书生活'
                  WHEN tags = '博士|学校信息' THEN '博士|学校信息'
                  WHEN tags = '博士|申请|总结' THEN '博士|申请总结'
                  WHEN tags = '博士|申请|答疑' THEN '博士|答疑'
                  WHEN tags = '签证|经验|加拿大' THEN '签证|签经'
                  WHEN tags = '签证|经验|欧洲' THEN '签证|签经'
                  WHEN tags = '签证|问题' THEN '签证|签经'
                  ELSE tags
              END
              WHERE tags IN (
                  'Master|申请|PS|Resume',
                  'Master|申请|总结',
                  'Master|申请|推荐信',
                  'Master|申请|答疑',
                  'Master|申请|面试|经验',
                  'MBA|申请|Essay',
                  'MBA|申请|Resume',
                  'MBA|申请|总结',
                  'MBA|申请|推荐信',
                  'MBA|申请|答疑',
                  'MBA|申请|面试|经验',
                  '博士|在读|经验',
                  '博士|学校信息',
                  '博士|申请|总结',
                  '博士|申请|答疑',
                  '签证|经验|加拿大',
                  '签证|经验|欧洲',
                  '签证|问题'
              );`);          

          // 读取并执行www.sql文件中的内容
          const fs = require('fs');
          const path = require('path');
          const wwwSqlPath = path.join(process.cwd(), 'www.sql');

          try {
              const wwwSqlContent = fs.readFileSync(wwwSqlPath, 'utf8');
              // 按行分割SQL语句
              const sqlStatements = wwwSqlContent.split('\n').filter((line: string) => line.trim() && !line.trim().startsWith('--'));

              for (const statement of sqlStatements) {
                  if (statement.trim()) {
                      await this.portalArticleEntity.query(statement.trim());
                  }
              }
          } catch (error) {
              console.error('Error reading or executing www.sql:', error);
          }

          await this.processTagsToTagsVal();
          
          // 初始化Elasticsearch索引并批量导入数据
          await this.initializeElasticsearchIndex();
      }
  }

  /**
   * 初始化Elasticsearch索引并从数据库批量导入数据
   */
  async initializeElasticsearchIndex() {
    // Define ES index name and mapping
    const ES_INDEX = 'portal_articles';

    // Delete existing index if it exists
    const indexExists = await this.es.client.indices.exists({ index: ES_INDEX });
    if (indexExists) {
        await this.es.client.indices.delete({ index: ES_INDEX });
    }

    // Create new index with mapping
    await this.es.client.indices.create({
        index: ES_INDEX,
        body: {
            mappings: {
                properties: {
                    id: { type: 'keyword' },
                    pic: { type: 'keyword' },
                    title: {
                        type: 'text',
                        analyzer: 'ik_max_word',
                    },
                    summary: {
                        type: 'text',
                        analyzer: 'ik_max_word',
                    },
                    content: {
                        type: 'text',
                        analyzer: 'ik_max_word',
                    },
                    datetime: { type: 'date' },
                    author: { type: 'keyword' },
                    category: { type: 'keyword' },
                    tags: {
                        type: 'keyword',
                        // 明确支持数组，提高查询性能
                        index: true
                    },
                    tagsValue: {
                        type: 'keyword',
                        index: true
                    },
                    majorTags: {
                        type: 'keyword',
                        index: true
                    },
                    majorTagsValue: {
                        type: 'keyword',
                        index: true
                    },
                    schoolTags: {
                        type: 'keyword',
                        index: true
                    },
                    schoolTagsValue: {
                        type: 'keyword',
                        index: true
                    },
                    type: {
                        type: 'keyword',
                        index: true
                    },
                    externalLink: {
                        type: 'keyword',
                        index: true
                    }
                }
            }
        }
    });

    // Batch index data from database
    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
        // Load articles from database in batches
        const articles = await this.portalArticleEntity
            .createQueryBuilder('article')
            .orderBy('article.id', 'ASC')
            .skip(offset)
            .take(batchSize)
            .getMany();

        if (articles.length === 0) {
            hasMore = false;
            break;
        }

        // Prepare ES documents
        const esDocuments = articles.map(article => ({
            id: article.id,
            pic: article.pic,
            title: article.title,
            summary: article.summary,
            content: article.content?.replace(/<[^>]+>/g, ''), // Remove HTML tags
            datetime: article.datetime,
            author: article.author,
            tags: this.convertStringToArray(article.tags),
            tagsValue: this.convertStringToArray(article.tagsVal),
            majorTags: this.convertStringToArray(article.majorTags),
            majorTagsValue: this.convertStringToArray(article.majorTagsVal),
            schoolTags: this.convertStringToArray(article.schoolTags),
            schoolTagsValue: this.convertStringToArray(article.schoolTagsVal),
            category: article.category,
            forumTid: article.forumTid,
            type: article.type,
            externalLink: article.externalLink
        }));

        // Bulk index to Elasticsearch
        await this.es.client.bulk({
            body: esDocuments.flatMap(doc => [
                { index: { _index: ES_INDEX, _id: doc.id } },
                doc
            ])
        });

        offset += batchSize;
        
        // If we got less than batch size, we're done
        if (articles.length < batchSize) {
            hasMore = false;
        }
    }
  }

  async getFullClassPath(classId: number): Promise<string[]> {
        const classInfo = await this.iwmsClass.findOne({
            where: { classID: classId }
        });

        if (!classInfo || !classInfo.class) {
            return [];
        }

        if (!classInfo.parentID) {
            return [classInfo.class];
        }

        const parentPath = await this.getFullClassPath(classInfo.parentID);
        return [...parentPath, classInfo.class];
    }

    async getTagFromCategories(classPath: string[]): Promise<string | null> {
        if (classPath.length < 2) return null;

        const parentCategory = classPath[0];
        const childCategory = classPath[1];

        const categories = {
            MBA: {
                "学校信息": "MBA|学校信息",
                "MBA排名": "MBA|排名",
                "申请FAQ": "MBA|申请|答疑",
                "申请策略": "MBA|申请|总结",
                "Resume": "MBA|申请|Resume",
                "推荐信": "MBA|申请|推荐信",
                "Essay写作": "MBA|申请|Essay",
                "MBA面试": "MBA|申请|面试|经验",
                "申请总结": "MBA|申请|总结"
            },
            Master: {
                "学校介绍": "Master|学校信息",
                "专业与排名": "Master|排名",
                "申请FAQ": "Master|申请|答疑",
                "推荐信": "Master|申请|推荐信",
                "PS/Resume": "Master|申请|PS|Resume",
                "商科面试": "Master|申请|面试|经验",
                "申请总结": "Master|申请|总结"
            },
            PhD: {
                "商学院介绍": "博士|学校信息",
                "排名": "博士|排名",
                "申请FAQ": "博士|申请|答疑",
                "推荐信": "博士|申请|推荐信",
                "申请总结": "博士|申请|总结",
                "PhD学习经验": "博士|在读|经验",
                "PHD面试": "博士|申请|面试|经验"
            },
            VISA: {
                "签证准备": "签证|准备",
                "签证问题集": "签证|问题",
                "签证策略": "签证|准备",
                "美国签证经验": "签证|经验|美国",
                "加拿大签证": "签证|经验|加拿大",
                "欧洲签证": "签证|经验|欧洲",
                "行前准备": ""
            },
            GMAT: {
                "GMAT入门": "GMAT",
                "GMAT报考": "GMAT|报名",
                "GMAT心经": "GMAT|考试经验",
                "GMAT数学": "GMAT|Quant",
                "GMAT阅读": "GMAT|RC",
                "GMAT逻辑": "GMAT|CR",
                "GMAT综合推理": "GMAT|DI"
            }
        };

        if (categories[parentCategory] && categories[parentCategory][childCategory]) {
            return categories[parentCategory][childCategory];
        }

        return null;
    }

    private processContentAndExtractTid(content: string): { cleanContent: string; tid: number } {
        // Remove font-size specifications
        let processed = content.replace(/font-size:\s*12px;/gi, '');

        // Find the style tag position using regex
        const stylePattern = /<style>\s*ignore_js_op|<p>-{5,}/i;
        const match = processed.match(stylePattern);
        if (!match) {
            return { cleanContent: processed, tid: 0 };
        }

        const styleIndex = match.index;

        // Extract the removed portion to find tid
        const removedPortion = processed.substring(styleIndex);
        const tidMatch = removedPortion.match(/thread-(\d+)-/);
        const tid = tidMatch ? parseInt(tidMatch[1], 10) : 0;

        // Return cleaned content (everything before style tag) and extracted tid
        return {
            cleanContent: processed.substring(0, styleIndex),
            tid
        };
    }

  /**
   * 根据tags字段处理tagsVal字段
   * 从tblTag表中根据property=3和main=1过滤数据，然后根据tags值找到对应的id保存到tagsVal
   */
  async processTagsToTagsVal() {
    try {
      // 1. 查询所有符合条件的标签
      const validTags = await this.tblTag.find({
        where: {
          property: 3,
          main: true
        }
      });

      // 创建标签名称到ID的映射
      const tagNameToIdMap = new Map<string, string>();
      validTags.forEach(tag => {
        if (tag.name) {
          tagNameToIdMap.set(tag.name.trim(), tag.id.toString());
        }
      });

      // 2. 查询所有有tags的文章
      const articles = await this.portalArticleEntity
        .createQueryBuilder('article')
        .where('article.tags IS NOT NULL AND article.tags != ""')
        .getMany();

      let processedCount = 0;
      const results = [];

      for (const article of articles) {
        if (article.tags) {
          const tagsVal = this.convertTagsToIds(article.tags, tagNameToIdMap);
          await this.portalArticleEntity.update(article.id, { tagsVal });
          processedCount++;
          results.push({
            id: article.id,
            tags: article.tags,
            tagsVal: tagsVal
          });
        }
      }

      return {
        processed: processedCount,
        total: articles.length,
        results: results,
        availableTags: validTags.map(tag => ({ id: tag.id, name: tag.name }))
      };

    } catch (error) {
      throw new Error(`处理标签转换时出错: ${error.message}`);
    }
  }

  /**
   * 将tags字符串转换为对应的ID字符串
   * @param tags 标签字符串，格式：GMAT|考试经验
   * @param tagNameToIdMap 标签名称到ID的映射
   * @returns 转换后的ID字符串，格式：id1|id2
   */
  private convertTagsToIds(tags: string, tagNameToIdMap: Map<string, string>): string {
    if (!tags || !tags.trim()) {
      return '';
    }

    // 按|分割标签
    const tagNames = tags.split('|').map(tag => tag.trim()).filter(tag => tag.length > 0);
    const matchedIds: string[] = [];

    tagNames.forEach(tagName => {
      const tagId = tagNameToIdMap.get(tagName);
      if (tagId) {
        matchedIds.push(tagId);
      }
    });

    return matchedIds.join('|');
  }

  /**
   * 获取所有可用的标签
   */
  async getAvailableTags(param: any) {
    return await this.tblTag.find({
      where: {
        property: param.property,
        main: true
      },
      select: ['id', 'name', 'order']
    });
  }

  /**
   * 获取 portalBannerIndex 的值
   * @returns 返回 portalBannerIndex 的数字值，如果不存在则返回 0
   */
  async getPortalBannerIndex(): Promise<number> {
    try {
      const value = await this.midwayCache.get(this.redisKey.portalBannerIndex);
      if (value === null || value === undefined) {
        return 0;
      }
      const numValue = parseInt(value.toString(), 10);
      return isNaN(numValue) ? 0 : numValue;
    } catch (error) {
      throw new Error(`获取 portalBannerIndex 失败: ${error.message}`);
    }
  }

  /**
   * 设置或更新 portalBannerIndex 的值
   * @param value 要设置的数字值
   * @returns 返回设置后的值
   */
  async setPortalBannerIndex(value: number): Promise<number> {
    try {
      if (!Number.isInteger(value)) {
        throw new Error('portalBannerIndex 必须是整数');
      }

      await this.midwayCache.set(this.redisKey.portalBannerIndex, value.toString());
      return value;
    } catch (error) {
      throw new Error(`设置 portalBannerIndex 失败: ${error.message}`);
    }
  }

  /**
   * 更新文章显示顺序
   * @param param 包含id和displayOrder的对象或对象数组
   */
  async updateDisplayOrder(param: any) {
    // 如果传入的是单个对象，转换为数组
    const items = Array.isArray(param) ? param : [param];

    for (const item of items) {
      if (!item.id) {
        throw new Error('缺少必要的id参数');
      }

      if (item.displayOrder === undefined || item.displayOrder === null) {
        throw new Error('缺少必要的displayOrder参数');
      }

      // 更新数据库
      await this.portalArticleEntity.update(item.id, {
        displayOrder: item.displayOrder,
      });
    }
  }

  /**
   * 传送wwwbuffer数据库中的Article数据到MySQL的portal_candidate_article表
   */
  async transferCandidateArticleData() {
    try {
      // 清空目标表
      await this.portalCandidateArticleEntity.clear();

      // 禁用外键检查和设置SQL模式以允许插入指定ID
      await this.portalCandidateArticleEntity.query('SET FOREIGN_KEY_CHECKS = 0');
      await this.portalCandidateArticleEntity.query('SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO"');

      // 从MSSQL数据库获取Article数据
      const articles = await this.wwwbufferArticle.find({
        where: { IsDeleted: false }
      });

      console.log(`Found ${articles.length} articles to transfer`);

      const batchSize = 1000;
      const transferredArticles = [];

      for (const article of articles) {
        const transferredArticle = {
          id: parseInt(article.ID), // 保持原始ID
          url: article.Url || '',
          title: article.Title || '',
          board: article.Board,
          author: article.Author,
          channel: article.Channel,
          tag: article.Tag,
          content: article.Content || '',
          refContent: article.RefContent,
          addedBy: article.AddedBy,
          approvedBy: article.ApprovedBy,
          createTime: article.CreateTime,
          approvedTime: article.ApprovedTime,
          wwwArticleID: article.WWWArticleID,
          fixNews: article.FixNews,          
          waid: article.WAID
        };

        transferredArticles.push(transferredArticle);

        // 批量保存
        if (transferredArticles.length === batchSize) {
          await this.portalCandidateArticleEntity.save(transferredArticles);
          console.log(`Transferred ${transferredArticles.length} candidate articles`);
          transferredArticles.length = 0;
        }
      }

      // 保存剩余的数据
      if (transferredArticles.length > 0) {
        await this.portalCandidateArticleEntity.save(transferredArticles);
        console.log(`Transferred final ${transferredArticles.length} candidate articles`);
      }

      // 重新启用外键检查和恢复SQL模式
      await this.portalCandidateArticleEntity.query('SET FOREIGN_KEY_CHECKS = 1');
      await this.portalCandidateArticleEntity.query('SET SQL_MODE = ""');

      console.log(`Successfully transferred ${articles.length} candidate articles from MSSQL to MySQL`);
      return { success: true, transferred: articles.length };

    } catch (error) {
      console.error('Error transferring candidate article data:', error);
      // 确保在出错时也恢复设置
      try {
        await this.portalCandidateArticleEntity.query('SET FOREIGN_KEY_CHECKS = 1');
        await this.portalCandidateArticleEntity.query('SET SQL_MODE = ""');
      } catch (resetError) {
        console.error('Error resetting SQL settings:', resetError);
      }
      throw new Error(`传送候选文章数据失败: ${error.message}`);
    }
  }

  /**
   * 传送wwwbuffer数据库中的WaitingArticle数据到MySQL的portal_waiting_article表
   */
  async transferWaitingArticleData() {
    try {
      // 清空目标表
      await this.portalWaitingArticleEntity.clear();

      // 禁用外键检查和设置SQL模式以允许插入指定ID
      await this.portalWaitingArticleEntity.query('SET FOREIGN_KEY_CHECKS = 0');
      await this.portalWaitingArticleEntity.query('SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO"');

      // 从MSSQL数据库获取WaitingArticle数据
      const waitingArticles = await this.wwwbufferWaitingArticle.find({
        where: { IsDeleted: false }
      });

      console.log(`Found ${waitingArticles.length} waiting articles to transfer`);

      const batchSize = 1000;
      const transferredWaitingArticles = [];

      for (const waitingArticle of waitingArticles) {
        const transferredWaitingArticle = {
          id: parseInt(waitingArticle.ID), // 保持原始ID
          threadID: waitingArticle.ThreadID,
          url: waitingArticle.Url || '',
          remark: waitingArticle.Remark,
          status: waitingArticle.Status,
          createTime: waitingArticle.CreateTime,          
        };

        transferredWaitingArticles.push(transferredWaitingArticle);

        // 批量保存
        if (transferredWaitingArticles.length === batchSize) {
          await this.portalWaitingArticleEntity.save(transferredWaitingArticles);
          console.log(`Transferred ${transferredWaitingArticles.length} waiting articles`);
          transferredWaitingArticles.length = 0;
        }
      }

      // 保存剩余的数据
      if (transferredWaitingArticles.length > 0) {
        await this.portalWaitingArticleEntity.save(transferredWaitingArticles);
        console.log(`Transferred final ${transferredWaitingArticles.length} waiting articles`);
      }

      // 重新启用外键检查和恢复SQL模式
      await this.portalWaitingArticleEntity.query('SET FOREIGN_KEY_CHECKS = 1');
      await this.portalWaitingArticleEntity.query('SET SQL_MODE = ""');

      console.log(`Successfully transferred ${waitingArticles.length} waiting articles from MSSQL to MySQL`);
      return { success: true, transferred: waitingArticles.length };

    } catch (error) {
      console.error('Error transferring waiting article data:', error);
      // 确保在出错时也恢复设置
      try {
        await this.portalWaitingArticleEntity.query('SET FOREIGN_KEY_CHECKS = 1');
        await this.portalWaitingArticleEntity.query('SET SQL_MODE = ""');
      } catch (resetError) {
        console.error('Error resetting SQL settings:', resetError);
      }
      throw new Error(`传送等待文章数据失败: ${error.message}`);
    }
  }

  /**
   * 传送所有wwwbuffer数据到MySQL
   */
  async transferAllWwwbufferData() {
    try {
      console.log('Starting transfer of all wwwbuffer data...');

      // 传送候选文章数据
      const candidateResult = await this.transferCandidateArticleData();
      console.log('Candidate articles transfer completed:', candidateResult);

      // 传送等待文章数据
      const waitingResult = await this.transferWaitingArticleData();
      console.log('Waiting articles transfer completed:', waitingResult);

      return {
        success: true,
        candidateArticles: candidateResult.transferred,
        waitingArticles: waitingResult.transferred,
        total: candidateResult.transferred + waitingResult.transferred
      };

    } catch (error) {
      console.error('Error transferring all wwwbuffer data:', error);
      throw new Error(`传送所有wwwbuffer数据失败: ${error.message}`);
    }
  }

  // CandidateArticle CRUD methods
  async candidateArticlePage(param: any) {
    const { page = 1, pageSize = 50, s, author, channel, tag, addedBy, approvedBy } = param;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.portalCandidateArticleEntity
      .createQueryBuilder('candidate')
      .orderBy('candidate.id', 'DESC')
      .skip(skip)
      .take(pageSize);

    if (s) {
      queryBuilder.where('candidate.title LIKE :title OR candidate.url LIKE :url', {
        title: `%${s}%`,
        url: `%${s}%`
      });
    }

    if (author) {
      queryBuilder.andWhere('candidate.author LIKE :author', { author: `%${author}%` });
    }

    if (channel) {
      queryBuilder.andWhere('candidate.channel LIKE :channel', { channel: `%${channel}%` });
    }

    if (tag) {
      queryBuilder.andWhere('candidate.tag LIKE :tag', { tag: `%${tag}%` });
    }

    if (addedBy) {
      queryBuilder.andWhere('candidate.addedBy LIKE :addedBy', { addedBy: `%${addedBy}%` });
    }

    if (approvedBy) {
      queryBuilder.andWhere('candidate.approvedBy LIKE :approvedBy', { approvedBy: `%${approvedBy}%` });
    }

    return await queryBuilder.getManyAndCount();
  }

  async candidateArticleFindOne(param: any) {
    return await this.portalCandidateArticleEntity.findOneBy({
      id: param.id,
    });
  }

  async candidateArticleCreate(param: any) {
    param.createTime = new Date();
    return await this.portalCandidateArticleEntity.save(param);
  }

  async candidateArticleUpdate(param: any) {
    const { id, ...updateData } = param;
    await this.portalCandidateArticleEntity.update(id, updateData);
    return await this.candidateArticleFindOne({ id });
  }

  async candidateArticleDelete(param: any) {
    return await this.portalCandidateArticleEntity.delete({ id: param.id });
  }

  // WaitingArticle CRUD methods
  async waitingArticlePage(param: any) {
    const { page = 1, pageSize = 50, s, status } = param;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.portalWaitingArticleEntity
      .createQueryBuilder('waiting')
      .orderBy('waiting.id', 'DESC')
      .skip(skip)
      .take(pageSize);

    if (s) {
      queryBuilder.where('waiting.url LIKE :url OR waiting.remark LIKE :remark', {
        url: `%${s}%`,
        remark: `%${s}%`
      });
    }

    if (status !== undefined && status !== null && status !== '') {
      queryBuilder.andWhere('waiting.status = :status', { status });
    }

    return await queryBuilder.getManyAndCount();
  }

  async waitingArticleFindOne(param: any) {
    return await this.portalWaitingArticleEntity.findOneBy({
      id: param.id,
    });
  }

  async waitingArticleCreate(param: any) {
    param.createTime = new Date();
    return await this.portalWaitingArticleEntity.save(param);
  }

  async waitingArticleUpdate(param: any) {
    const { id, ...updateData } = param;
    await this.portalWaitingArticleEntity.update(id, updateData);
    return await this.waitingArticleFindOne({ id });
  }

  async waitingArticleDelete(param: any) {
    return await this.portalWaitingArticleEntity.delete({ id: param.id });
  }

  /**
   * 获取文章信息
   * 从给定的URL获取文章的标题、作者、版块和内容信息
   */
  async getArticleInfo(param: any) {
    try {
      const { url } = param;

      if (!url) {
        throw new Error('URL参数不能为空');
      }

      // 使用axios获取网页内容
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      const htmlContent = response.data;

      // 使用cheerio解析HTML
      const $ = cheerio.load(htmlContent);

      // 提取标题 - 查找id为thread_subject的a标签
      let title = '';
      const titleElement = $('a#thread_subject');
      if (titleElement.length > 0) {
        title = titleElement.text().trim();
      }

      // 提取作者 - 查找class为xw1的a标签
      let author = '';
      const authorElement = $('a.xw1').first();
      if (authorElement.length > 0) {
        author = authorElement.text().trim();
      }

      // 提取版块信息 - 查找href包含forum-的a标签
      let board = '';
      let boardUrl = '';
      $('a').each((_, element) => {
        const href = $(element).attr('href');
        if (href && href.match(/forum-(\d+)-(\d+)\.html/)) {
          board = $(element).text().trim();
          boardUrl = href;
          return false; // 找到第一个就停止
        }
      });

      // 提取内容 - 查找class为pcb的div下的内容
      let content = '';
      const contentElement = $('.pcb').first().find('div table tr td').first();
      if (contentElement.length > 0) {
        content = contentElement.html() || '';
        // 处理图片路径，添加域名前缀
        content = this.processImageUrls(content);
      }

      // 生成参考内容
      const refContent = this.generateRefContent(board, url, boardUrl);

      return {
        success: true,
        data: {
          title: title,
          author: author,
          board: board,
          content: content,
          refContent: refContent
        }
      };

    } catch (error) {
      console.error('获取文章信息失败:', error);
      return {
        success: false,
        message: error.message || '获取文章信息失败'
      };
    }
  }

  /**
   * 处理图片URL，添加域名前缀
   */
  private processImageUrls(content: string): string {
    if (!content) return '';

    // 去掉隐藏的textarea标签，但保留其内容
    content = content.replace(
      /<textarea[^>]*class="[^"]*markdownContent[^"]*vditor-reset[^"]*"[^>]*>([\s\S]*?)<\/textarea>/gi,
      '$1'
    );

    // 去掉包含ignore_js_op和duceapp_attachbox的style标签
    content = content.replace(
      /<style[^>]*>[\s\S]*?ignore_js_op[\s\S]*?duceapp_attachbox[\s\S]*?<\/style>/gi,
      ''
    );

    // 处理HTML中的img标签src属性
    content = content.replace(
      /<img([^>]*)\ssrc="(?!https?:\/\/)([^"]+)"([^>]*)>/gi,
      '<img$1 src="https://forum.chasedream.com/$2"$3>'
    );

    // 处理markdown格式的图片
    content = content.replace(
      /!\[([^\]]*)\]\((?!https?:\/\/)([^)]+)\)/g,
      '![$1](https://forum.chasedream.com/$2)'
    );

    return content;
  }

  /**
   * 版块参考内容映射
   * 对应C#中的AppConst.REF_CONTENT
   */
  private readonly REF_CONTENT_MAP = new Map<string, { url: string; text: string }>([
    ['Guide', { url: 'https://forum.chasedream.com/Guide/list-1.html', text: '梦网导航' }],
    ['Announcement', { url: 'https://forum.chasedream.com/Announcement/list-1.html', text: '公告区' }],
    ['Career_General', { url: 'https://forum.chasedream.com/Career_General/list-1.html', text: 'Career General' }],
    ['working_abroad', { url: 'https://forum.chasedream.com/working_abroad/list-1.html', text: 'Working Abroad' }],
    ['Business_Library', { url: 'https://forum.chasedream.com/Business_Library/list-1.html', text: 'Business Library' }],
    ['Professional_Certificate', { url: 'https://forum.chasedream.com/Professional_Certificate/list-1.html', text: 'Professional Certificate' }],
    ['Investment_Banking', { url: 'https://forum.chasedream.com/Investment_Banking/list-1.html', text: 'I-Banking' }],
    ['Consulting', { url: 'https://forum.chasedream.com/Consulting/list-1.html', text: 'Consulting' }],
    ['Auditing', { url: 'https://forum.chasedream.com/Auditing/list-1.html', text: 'Auditing' }],
    ['Entrepreneurship', { url: 'https://forum.chasedream.com/Entrepreneurship/list-1.html', text: 'Entrepreneurship' }],
    ['Corporate_Finance', { url: 'https://forum.chasedream.com/Corporate_Finance/list-1.html', text: 'Corporate Finance' }],
    ['Marketing', { url: 'https://forum.chasedream.com/Marketing/list-1.html', text: 'Marketing' }],
    ['Supply_Chain', { url: 'https://forum.chasedream.com/Supply_Chain/list-1.html', text: 'Supply Chain' }],
    ['Human_Resource', { url: 'https://forum.chasedream.com/Human_Resource/list-1.html', text: 'Human Resource' }],
    ['Healthcare_Pharmaceutical', { url: 'https://forum.chasedream.com/Healthcare_Pharmaceutical/list-1.html', text: 'Healthcare/Pharmaceutical' }],
    ['Real_Estate', { url: 'https://forum.chasedream.com/Real_Estate/list-1.html', text: 'Real Estate' }],
    ['Nonprofit', { url: 'https://forum.chasedream.com/Nonprofit/list-1.html', text: 'Nonprofit' }],
    ['Business_School_Life', { url: 'https://forum.chasedream.com/Business_School_Life/list-1.html', text: 'B-School学习与生活' }],
    ['VISA', { url: 'https://forum.chasedream.com/VISA/list-1.html', text: '签证专区' }],
    ['North_American_MBA', { url: 'https://forum.chasedream.com/North_American_MBA/list-1.html', text: '北美MBA申请区' }],
    ['European_MBA', { url: 'https://forum.chasedream.com/European_MBA/list-1.html', text: '欧洲MBA申请区' }],
    ['Asian_Pacific_MBA', { url: 'https://forum.chasedream.com/Asian_Pacific_MBA/list-1.html', text: '亚太MBA申请区' }],
    ['Master', { url: 'https://forum.chasedream.com/Master/list-1.html', text: '商学院Master申请区' }],
    ['PhD', { url: 'https://forum.chasedream.com/PhD/list-1.html', text: '商学院Ph.D申请区' }],
    ['GMAT_Preparation', { url: 'https://forum.chasedream.com/GMAT_Preparation/list-1.html', text: '走出GMAT困境' }],
    ['GMAT_Math', { url: 'https://forum.chasedream.com/GMAT_Math/list-1.html', text: 'GMAT机经专区+数学讨论' }],
    ['GMAT_SC', { url: 'https://forum.chasedream.com/GMAT_SC/list-1.html', text: 'GMAT语法专区' }],
    ['GMAT_CR', { url: 'https://forum.chasedream.com/GMAT_CR/list-1.html', text: 'GMAT逻辑专区' }],
    ['GMAT_RC', { url: 'https://forum.chasedream.com/GMAT_RC/list-1.html', text: 'GMAT阅读专区' }],
    ['GMAT_AWA', { url: 'https://forum.chasedream.com/GMAT_AWA/list-1.html', text: 'GMAT AWA写作专区' }],
    ['GMAT_IR', { url: 'https://forum.chasedream.com/GMAT_IR/list-1.html', text: 'GMAT Integrated Reasoning' }],
    ['GMAT_Guide', { url: 'https://forum.chasedream.com/GMAT_Guide/list-1.html', text: 'GMAT备考资料下载区' }],
    ['GRE_Prep', { url: 'https://forum.chasedream.com/GRE_Prep/list-1.html', text: '新GRE综合讨论区' }],
    ['forum78', { url: 'https://forum.chasedream.com/forum78/list-1.html', text: '新GRE机经专区' }],
    ['GRE_AW', { url: 'https://forum.chasedream.com/GRE_AW/list-1.html', text: '新GRE作文专区' }],
    ['GRE_Sub', { url: 'https://forum.chasedream.com/GRE_Sub/list-1.html', text: 'GRE Subject讨论区' }],
    ['GRE_Material', { url: 'https://forum.chasedream.com/GRE_Material/list-1.html', text: 'GRE备考资料下载' }],
    ['sci_eng', { url: 'https://forum.chasedream.com/sci_eng/list-1.html', text: '理工科申请专区' }],
    ['TOEFL_iBT', { url: 'https://forum.chasedream.com/TOEFL_iBT/list-1.html', text: 'TOEFL iBT专区' }],
    ['TOEFL_Writing', { url: 'https://forum.chasedream.com/TOEFL_Writing/list-1.html', text: 'TOEFL 写作专区' }],
    ['TOEFL_CBT_PBT', { url: 'https://forum.chasedream.com/TOEFL_CBT_PBT/list-1.html', text: 'TOEFL CBT与笔考专区' }],
    ['English', { url: 'https://forum.chasedream.com/English/list-1.html', text: '英语实力提高区' }],
    ['Undergraduate', { url: 'https://forum.chasedream.com/Undergraduate/list-1.html', text: '北美大学本科申请交流区' }],
    ['LSAT', { url: 'https://forum.chasedream.com/LSAT/list-1.html', text: 'LSAT综合讨论区' }],
    ['JD_LLM_JSD', { url: 'https://forum.chasedream.com/JD_LLM_JSD/list-1.html', text: '法学院申请交流区' }],
    ['Download', { url: 'https://forum.chasedream.com/Download/list-1.html', text: '下载专区' }],
    ['Flea_Market', { url: 'https://forum.chasedream.com/Flea_Market/list-1.html', text: '考试资料交流区' }],
    ['Chit_Chat', { url: 'https://forum.chasedream.com/Chit_Chat/list-1.html', text: '梦网情缘' }],
    ['Sports', { url: 'https://forum.chasedream.com/Sports/list-1.html', text: '火热体坛＋健康与美' }],
    ['forum12', { url: 'https://forum.chasedream.com/forum12/list-1.html', text: '大话贴图' }],
    ['PC', { url: 'https://forum.chasedream.com/PC/list-1.html', text: '玩转PC' }],
    ['Gathering', { url: 'https://forum.chasedream.com/Gathering/list-1.html', text: '相聚梦网' }]
  ]);

  /**
   * 生成参考内容
   * 参考C#中的FunctionExtensions.RefContent方法
   */
  private generateRefContent(board: string, url: string, boardUrl: string): string {
    if (!board || !url) {
      return '';
    }

    // 查找对应的版块信息
    const boardInfo = this.REF_CONTENT_MAP.get(board.toLowerCase()) ||
                     Array.from(this.REF_CONTENT_MAP.entries()).find(([, value]) =>
                       value.text.toLowerCase() === board.toLowerCase()
                     )?.[1];

    let refContent = '';

    // 添加原文引用信息
    refContent += '<p>';
    refContent += '原文引自：';
    refContent += '<br />';
    refContent += `<a href="${url}">${url}</a>`;
    refContent += '</p>';

    // 添加讨论版块信息
    refContent += '<p>';
    refContent += `参与讨论及查看更多的相关文章请访问【${boardInfo ? boardInfo.text : board}】`;
    refContent += '<br />';

    // 构建版块链接
    let discussionUrl = '';
    if (boardUrl) {
      // 从原始URL中提取基础URL
      const baseUrl = url.substring(0, url.lastIndexOf('/') + 1);
      discussionUrl = baseUrl + boardUrl.replace(/^"/, '').replace(/"$/, '');
    } else if (boardInfo) {
      discussionUrl = boardInfo.url;
    }

    if (discussionUrl) {
      refContent += `<a href="${discussionUrl}">${discussionUrl}</a>`;
    }

    refContent += '</p>';

    return refContent;
  }

  /**
   * 将用|分隔的字符串转换为数组
   * @param str 输入字符串，格式：GMAT|考试经验 或 817|829
   * @returns 转换后的数组，如果输入为空则返回空数组
   */
  private convertStringToArray(str: string): string[] {
    if (!str || !str.trim()) {
      return [];
    }

    return str.split('|').map(item => item.trim()).filter(item => item.length > 0);
  }
}
