import {
  Provide,
  Body,
  Inject,
  Post,
  Get,
  Query,
  App,
  AL<PERSON>,
  <PERSON><PERSON>,
  Para<PERSON>,
} from '@midwayjs/decorator';
import {
  Cool<PERSON><PERSON>roller,
  BaseController,
  Cool<PERSON><PERSON>,
  CoolUrlTag,
  CoolTag,
  TagTypes,
  RESCODE,
  CoolCache,
} from '@cool-midway/core';

import { LoginDTO } from '../../dto/login';
import { BaseSysLoginService } from '../../service/sys/login';
import { BaseSysParamService } from '../../service/sys/param';
import { Application, Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { Utils } from '../../../../comm/utils';
import * as _ from 'lodash';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CcCommonSearchLog } from '../../entity/forum/CcCommonSearchLog';
import { CcCommonMember } from '../../entity/forum/CcCommonMember';
import { CommonForumService } from '../../service/common/forum';
import { ILogger } from '@midwayjs/logger';
import { MailEntity } from '../../entity/mail/mail';
import { EmailService } from '../../service/common/email';
import { Files, Fields, Config } from '@midwayjs/core';
import * as fse from 'fs-extra';
import { ChatgptSessionUploadEntity } from '../../entity/chatgpt/chatgptSessionUpload';
import { CommonHotspotService } from '../../service/common/hotspot';
import { ChatgptService } from '../../service/common/chatgpt';
import { DouyinService } from '../../service/common/douyin';
const path = require('path');
const fs = require('fs');
import { getSignature, decrypt } from '@wecom/crypto';
import { FileBox } from 'file-box';
import { CommonSougouService } from '../../service/common/sougou';
import { TblSchoolDic } from '../../entity/event/TblSchoolDic';
import { TblUpload } from '../../entity/event/TblUpload';
import { BaseSysConfService } from '../../service/sys/conf';
import { EventService } from '../../service/common/event';
import { PortalService } from '../../service/common/portal';

/**
 * 不需要登录的后台接口
 */
@Provide()
@CoolController({ description: '开放接口' })
@CoolUrlTag()
export class BaseOpenController extends BaseController {
  @Config('wecom')
  wecom;

  @Inject()
  baseSysLoginService: BaseSysLoginService;

  @Inject()
  baseSysParamService: BaseSysParamService;

  @InjectEntityModel(CcCommonSearchLog, 'forum')
  ccCommonSearchLog: Repository<CcCommonSearchLog>;

  @InjectEntityModel(CcCommonMember, 'forum')
  ccCommonMember: Repository<CcCommonMember>;

  @Inject()
  commonForumService: CommonForumService;

  @Inject()
  emailService: EmailService;

  @Inject()
  commonHotspotService: CommonHotspotService;

  @Inject()
  chatgptService: ChatgptService;

  @Inject()
  douyinService: DouyinService;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  eventService: EventService;

  @Inject()
  portalService: PortalService;

  @InjectEntityModel(MailEntity)
  mailEntity: Repository<MailEntity>;

  @InjectEntityModel(ChatgptSessionUploadEntity)
  chatgptSessionUploadEntity: Repository<ChatgptSessionUploadEntity>;

  @InjectEntityModel(TblSchoolDic, 'id')
  tblSchoolDic: Repository<TblSchoolDic>;

  @InjectEntityModel(TblUpload, 'id')
  tblUpload: Repository<TblUpload>;

  @Inject()
  ctx: Context;

  @App()
  app: Application;

  @Inject()
  eps: CoolEps;

  @Inject()
  utils: Utils;

  @Inject()
  baseDir;

  @Logger()
  logger: ILogger;

  @Inject()
  commonSougouService: CommonSougouService;

  /**
   * 实体信息与路径
   * @returns
   */
  @CoolTag(TagTypes.IGNORE_TOKEN)
  @Get('/eps', { summary: '实体信息与路径' })
  public async getEps() {
    return this.ok(this.eps.admin);
  }

  /**
   * 根据配置参数key获得网页内容(富文本)
   */
  @CoolTag(TagTypes.IGNORE_TOKEN)
  @Get('/html', { summary: '获得网页内容的参数值' })
  async htmlByKey(@Query('key') key: string) {
    this.ctx.body = await this.baseSysParamService.htmlByKey(key);
  }

  /**
   * 登录
   * @param login
   */
  @CoolTag(TagTypes.IGNORE_TOKEN)
  @Post('/login', { summary: '登录' })
  @Validate()
  async login(@Body() login: LoginDTO) {
    return this.ok(await this.baseSysLoginService.login(login));
  }

  /**
   * 获得验证码
   */
  @CoolTag(TagTypes.IGNORE_TOKEN)
  @Get('/captcha', { summary: '验证码' })
  async captcha(
    @Query('type') type: string,
    @Query('width') width: number,
    @Query('height') height: number,
    @Query('color') color: string
  ) {
    return this.ok(
      await this.baseSysLoginService.captcha(type, width, height, color)
    );
  }

  /**
   * 刷新token
   */
  @CoolTag(TagTypes.IGNORE_TOKEN)
  @Get('/refreshToken', { summary: '刷新token' })
  async refreshToken(@Query('refreshToken') refreshToken: string) {
    try {
      const token = await this.baseSysLoginService.refreshToken(refreshToken);
      return this.ok(token);
    } catch (e) {
      this.ctx.status = 401;
      this.ctx.body = {
        code: RESCODE.COMMFAIL,
        message: '登录失效~',
      };
    }
  }

  /**
   * 检查论坛登录状态
   */
  @CoolTag(TagTypes.IGNORE_TOKEN)
  @Get('/checkForumLogin', { summary: '检查论坛登录状态' })
  async checkForumLogin() {
    let uid = 0;

    if (this.app.getEnv() === 'prod') {
      const userinfo = this.utils.getUserinfoFromCookie(this.ctx);
      uid = parseInt(userinfo[1], 10);
    } else {
      uid = 219;
    }

    return this.ok(await this.baseSysLoginService.loginWithCookie(uid));
  }

  @CoolTag(TagTypes.IGNORE_TOKEN)
  @Post('/ttt')
  async ttt(@Body(ALL) param: any) {
    // await this.commonForumService.initForumIndex();
    // await this.commonForumService.initForumIndexData();
    // await this.portalService.initializeElasticsearchIndex();
    // await this.portalService.transferAllWwwbufferData();

    return this.ok({
      success: true,
    });
  }

  @Get('/wecom')
  async verifyWecomURL(@Query(ALL) param: any) {
    const msg_signature = decodeURIComponent(param.msg_signature);
    const timestamp = decodeURIComponent(param.timestamp);
    const nonce = decodeURIComponent(param.nonce);
    const echostr = decodeURIComponent(param.echostr);

    const newSign = getSignature(
      this.wecom.customApp.token,
      timestamp,
      nonce,
      echostr
    );

    if (newSign == msg_signature) {
      const { message } = decrypt(this.wecom.customApp.EncodingAESKey, echostr);

      this.ctx.status = 200;
      this.ctx.body = message;
    } else {
      this.ctx.status = 200;
      this.ctx.body = 'validate signature is failure';
    }
  }

  @Post('/wecom')
  async receiveWecomMessage(@Body(ALL) param: any) {
    console.log(param);

    this.ctx.status = 200;
    this.ctx.body = '';
  }

  @Get('/searchRecommend')
  async SearchRecommend(@Query('kw') kw: string) {
    if (!_.isEmpty(kw?.trim())) {
      return this.ok(await this.commonForumService.searchRecommend(kw));
    } else {
      return this.ok({
        success: true,
      });
    }
  }

  @Post('/search')
  async ForumSearch(@Query() qs) {
    if (!_.isEmpty(qs?.kw?.trim())) {
      const has = await this.commonForumService.hasSensitive(qs.kw);
      if (has) {
        return this.ok({
          success: true,
        });
      }

      return this.ok(await this.commonForumService.search(qs));
    } else {
      return this.ok({
        success: true,
      });
    }
  }

  @Get('/wwwCDG')
  async wwwCDG() {
    return this.ok(await this.commonForumService.forumAds4WWW());
  }

  @Post('/sendmail')
  async sendmail(@Body(ALL) param: any) {
    if (param.token !== '0C075B60D27D5B05E2D841334DC6CB056') {
      return this.ok({
        success: false,
      });
    }

    await this.emailService.sendmail({
      toemail: param.toemail,
      subject: param.subject,
      message: param.message,
    });

    return this.ok({
      success: true,
    });
  }

  @CoolCache(5000)
  @Get('/ieseBlogs')
  async ieseBlogs(@Query('page') page?: number,    
  @Query('page_size') page_size?: number,
  @Query('tag_id') tag_id?: number,) {    
    return await this.eventService.ieseBlogs(page, page_size, tag_id);
  }

  @CoolCache(5000)
  @Get('/blogShow/:id')
  async blogShow(@Param('id') id: string) {
    return this.eventService.blogShow(id);
  }

  @CoolCache(5000)
  @Get('/ieseEvents')
  async ieseEvents(@Query('page') page?: number,    
  @Query('page_size') page_size?: number,
  @Query('datetime') datetime?: string,
  @Query('majors[]') majors?: any[],
  @Query('locations[]') locations?: any[]) {    
    return this.eventService.ieseEvents(page, page_size, datetime, majors, locations);
  }

  @CoolCache(5000)
  @Get('/eventShow/:id')
  async eventShow(@Param('id') id: string) {
    return this.eventService.eventShow(id);
  }

  @CoolCache(5000)
  @Get('/searchItems')
  async searchItems() {
    return this.eventService.searchItems();
  }

  @Get('/previewGptImg/:img')
  async previewGptImg(@Param('img') img: string) {
    const file = `${this.baseDir}/../upload/chatgpt/${img.replaceAll(
      '-',
      '/'
    )}`;

    const image = fse.readFileSync(file);
    this.ctx.response.type = 'image/png';

    this.ctx.body = image;
  }

  @Get('/previewGptMp3/:file')
  async previewGptMp3(@Param('file') file: string) {
    const path = `${this.baseDir}/../upload/chatgpt/${file.replaceAll(
      '-',
      '/'
    )}`;

    const mp3 = fse.readFileSync(path);
    this.ctx.response.type = 'audio/mpeg';

    this.ctx.body = mp3;
  }

  @Post('/upload4GPT')
  async upload4GPT(@Files() files, @Fields() fields) {
    const urls = [];

    for (const file of files) {
      const yearMonthDate = this.utils.yearMonthDate();
      const filePath = `${this.baseDir}/../upload/chatgpt/${yearMonthDate}`;
      const fileName = `${this.utils.today('YYYYMMDDHHmmssSSS')}`;
      const ext = path.extname(file.filename);
      const dest = `${filePath}${fileName}${ext}`;

      await fse.move(file.data, dest);

      const voiceExt = [
        '.mp3',
        '.mp4',
        '.mpeg',
        '.mpga',
        '.m4a',
        '.wav',
        '.webm',
      ];

      const localPreviewPre = voiceExt.includes(ext)
        ? 'http://localhost:9000/dev/admin/base/open/previewGptMp3/'
        : 'http://localhost:9000/dev/admin/base/open/previewGptImg/';

      const url =
        this.ctx.app.getEnv() === 'prod'
          ? `https://connect.chasedream.com/upload/chatgpt/${yearMonthDate}${fileName}${ext}`
          : `${localPreviewPre}${yearMonthDate.replaceAll(
              '/',
              '-'
            )}${fileName}${ext}`;

      const model = await this.chatgptSessionUploadEntity.save({
        url,
      });

      urls.push(model);
    }

    return this.ok({
      success: true,
      urls,
    });
  }

  @Post('/upload4Event')
  async upload4Event(@Files() files, @Query(ALL) param: any) {
    if (!param.school_id) throw new Error('缺少参数');

    const school = await this.tblSchoolDic.findOneBy({ id: param.school_id });
    const type = param.type;

    let attach: any = {};
    let fileName = '';
    let random = '';

    for (const file of files) {
      const yearMonth = this.utils.yearMonth();
      const filePath = `${this.baseDir}/../upload/events/event-IMG/${school.directory}/${yearMonth}`;

      if (type === 'core') {
        random = this.utils.randomstring(8);
        fileName = `C-${random}`;
      } else {
        random = this.utils.randomstring(6);
        fileName = `${type}-${random}`;
      }

      const ext = path.extname(file.filename);
      const dest = `${filePath}${fileName}${ext}`;

      await fse.move(file.data, dest);

      attach = await this.tblUpload.save({
        school_id: school.id,
        category: 'event',
        original: file.filename,
        fullpath: `/events/event-IMG/${school.directory}/${yearMonth}${fileName}${ext}`,
        type: file.mimeType,
        created_at: new Date(),
      });
    }

    return this.ok({
      success: true,
      image: `https://static.chasedream.com${attach.fullpath}`,
    });
  }

  @Post('/upload4EventPush2')
  async upload4EventPush2(@Files() files, @Query(ALL) param: any) {
    let attach: any = {};
    let fileName = this.utils.randomstring(6);

    for (const file of files) {
      const yearMonth = this.utils.yearMonth();
      const filePath = `${this.baseDir}/../upload/events/event-IMG/Push2/${yearMonth}`;

      const ext = path.extname(file.filename);
      const dest = `${filePath}${fileName}${ext}`;

      await fse.move(file.data, dest);

      attach = await this.tblUpload.save({
        category: 'event',
        original: file.filename,
        fullpath: `/events/event-IMG/Push2/${yearMonth}${fileName}${ext}`,
        type: file.mimeType,
        created_at: new Date(),
      });
    }

    return this.ok({
      success: true,
      image: `https://static.chasedream.com${attach.fullpath}`,
    });
  }

  @Post('/upload4PortalPic')
  async upload4PortalPic(@Files() files, @Query(ALL) param: any) {
    let attach: any = {};
    let fileName = this.utils.randomstring(10);

    for (const file of files) {
      const yearMonthDate = this.utils.yearMonthDate();
      const filePath = `${this.baseDir}/../upload/portal/pic/${yearMonthDate}`;

      const ext = path.extname(file.filename);
      const dest = `${filePath}${fileName}${ext}`;

      await fse.move(file.data, dest);

      attach = await this.tblUpload.save({
        category: 'portal',
        original: file.filename,
        fullpath: `/portal/pic/${yearMonthDate}${fileName}${ext}`,
        type: file.mimeType,
        created_at: new Date(),
      });
    }

    return this.ok({
      success: true,
      image: `https://static.chasedream.com${attach.fullpath}`,
    });
  }

  @Post('/upload4IESEPic')
  async upload4IESEPic(@Files() files, @Query(ALL) param: any) {
    let attach: any = {};
    let fileName = this.utils.randomstring(10);

    for (const file of files) {
      const yearMonthDate = this.utils.yearMonthDate();
      const filePath = `${this.baseDir}/../upload/iese/pic/${yearMonthDate}`;

      const ext = path.extname(file.filename);
      const dest = `${filePath}${fileName}${ext}`;

      await fse.move(file.data, dest);

      attach = await this.tblUpload.save({
        category: 'iese',
        original: file.filename,
        fullpath: `/iese/pic/${yearMonthDate}${fileName}${ext}`,
        type: file.mimeType,
        created_at: new Date(),
      });
    }

    return this.ok({
      success: true,
      image: `https://static.chasedream.com${attach.fullpath}`,
    });
  }

  @Post('/upload4EventSchoolLogo')
  async upload4EventSchoolLogo(@Files() files, @Query(ALL) param: any) {
    const filePath = `${this.baseDir}/../upload/events/event-IMG/${param.directory}`;
    const logoPath = `${filePath}/logo`;
    const fullPath = `${logoPath}/1024.png`;

    if (param?.directory?.length === 0 || !fs.existsSync(filePath)) {
      throw new Error('目录不存在，请联系管理员创建目录');
    }

    fse.emptyDirSync(logoPath);

    const file = files[0];
    await fse.move(file.data, fullPath, { overwrite: true });

    await this.utils.resizeSpec(
      fullPath,
      logoPath,
      [
        {
          w: 512,
          h: null,
        },
        {
          w: 256,
          h: null,
        },
        {
          w: 128,
          h: null,
        },
        {
          w: 60,
          h: null,
        },
        {
          w: 21,
          h: null,
        },
      ],
      'png'
    );

    return this.ok({
      success: true,
      image: `https://static.chasedream.com/events/event-IMG/${param.directory}/logo/1024.png`,
    });
  }

  @Post('/upload4Wechat')
  async upload4Wechat(@Files() files, @Fields() fields) {
    for (const file of files) {
      const yearMonthDate = this.utils.yearMonthDate();
      const filePath = `${this.baseDir}/../upload/wechat/message/${yearMonthDate}`;
      const fileName = `${this.utils.today('YYYYMMDDHHmmssSSS')}`;
      const ext = path.extname(file.filename);
      const dest = `${filePath}${fileName}${ext}`;

      await fse.move(file.data, dest);

      const room = await global.wechatBot.Room.find({ id: fields.roomId });
      const fileBox = FileBox.fromFile(dest);
      await room.say(fileBox);
    }

    return this.ok({
      success: true,
    });
  }

  @Post('/hotspot', { summary: 'Hotspot列表' })
  async hotspot() {
    return this.ok(await this.commonHotspotService.info());
  }

  @Post('/gptsAuth', { summary: 'GPTs验证' })
  async gptsAuth(@Body(ALL) param: any) {
    return this.ok(await this.chatgptService.gptsAuth(param));
  }

  @Post('/appFeatureSwitch')
  async appFeatureSwitch(@Body(ALL) param: any) {
    const val = await this.baseSysConfService.getValue('appFeatureSwitch');

    return JSON.parse(val);
  }

  @Get('/showRelease')
  async showRelease(@Query(ALL) param: any) {
    const data = await this.eventService.showRelease();

    const res = {
      msg: 'success',
      data,
    };

    if (param.callback) {
      return `${param.callback}(${JSON.stringify(res)})`;
    }

    return res;
  }

  @Get('/showCalendar')
  async showCalendar(@Query(ALL) param: any) {
    const data = await this.eventService.showCalendar(param);

    const res = {
      msg: 'success',
      data,
    };

    if (param.callback) {
      return `${param.callback}(${JSON.stringify(res)})`;
    }

    return res;
  }

  @Get('/push1_www_position')
  async push1WwwPosition(@Query(ALL) param: any) {
    const data = await this.eventService.push1WwwPosition(param);

    const res = {
      msg: 'success',
      data: {
        push1_www_position: data.value,
      },
    };

    if (param.callback) {
      return `${param.callback}(${JSON.stringify(res)})`;
    }

    return res;
  }

  @Get('/www_hot')
  async wwwHot(@Query(ALL) param: any) {
    const data = await this.eventService.wwwHot(param);

    const res = {
      msg: 'success',
      data: {
        www_hot: data.value,
      },
    };

    if (param.callback) {
      return `${param.callback}(${JSON.stringify(res)})`;
    }

    return res;
  }
}
